/**
 * React版本的打招呼页面组件
 * 重点：保持与Lit版本完全一致的事件路由逻辑
 * 使用组件化架构，拆分为独立的子组件
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';

import { Card, CardContent } from '../components/ui/card';
import { PageState, PageStateChangeEvent } from '../core/PageStateManager';
import { WebSDK } from '../core/WebSDK';
import { generateUUID } from '../utils/helpers';
import { Logger } from '../utils/Logger';

// 导入子组件
import { AllServices } from './components/AllServices';
import { AutoplayBlockedNotification } from './components/AutoplayBlockedNotification';
import { AvatarFeatures } from './components/AvatarFeatures';
import { ChatWindow } from './components/ChatWindow';
import { QuickActions } from './components/QuickActions';
import { RecommendationCards } from './components/RecommendationCards';
import { TopNavigation } from './components/TopNavigation';
import { VideoPlayer, VideoPlayerRef } from './components/VideoPlayer';

// 导入全局样式
import '../styles/globals.css';

interface GreetingPageProps {
  sdk: WebSDK;
  title?: string;
  welcomeMessage?: string;
  showCloseButton?: boolean;
  onClose?: () => void;
}

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  sessionId: string;
  status: 'pending' | 'sending' | 'sent' | 'delivered' | 'failed' | 'streaming';
  createdAt: number;
  updatedAt: number;
}

export const GreetingPageReact: React.FC<GreetingPageProps> = ({ sdk, onClose }) => {
  // 状态管理（与Lit版本保持一致）
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [microphoneMode, setMicrophoneMode] = useState<'button' | 'lip'>('lip'); // 默认为唇动收音模式
  const [language, setLanguage] = useState<'mandarin' | 'chuanyu'>('mandarin');
  const [layoutMode, setLayoutMode] = useState<'standard' | 'care'>('standard');
  const [showAutoplayNotification, setShowAutoplayNotification] = useState(false);

  // 页面状态管理
  const [pageState, setPageState] = useState<PageState>('WaitCardPage');

  // 流式消息状态管理
  const [streamingMessages, setStreamingMessages] = useState<
    Map<string, { content: string; messageId: string }>
  >(new Map());

  // 会话管理（使用纯UUID）
  const sessionIdRef = useRef(generateUUID());
  const loggerRef = useRef(Logger.getInstance({ prefix: 'GreetingPageReact' }));
  const videoPlayerRef = useRef<VideoPlayerRef>(null);

  // 添加消息（与Lit版本保持一致）
  const addMessage = (message: Message) => {
    setMessages(prev => [...prev, message]);
  };

  // 更新流式消息显示（参考ChatWidget实现）
  const updateStreamingMessage = useCallback(
    (messageChunk: string, requestId: string, isPartial: boolean) => {
      loggerRef.current.debug('🔄 更新流式消息显示', {
        requestId,
        messageChunk: messageChunk.substring(0, 50) + '...',
        isPartial,
      });

      // 使用当前的streamingMessages状态进行调试日志
      loggerRef.current.debug('当前流式消息状态', {
        currentStreamingCount: streamingMessages.size,
        requestId,
      });

      setStreamingMessages(prevStreamingMessages => {
        const newStreamingMessages = new Map(prevStreamingMessages);
        let streamingState = newStreamingMessages.get(requestId);

        if (!streamingState) {
          // 创建新的流式消息
          const messageId = `streaming_${requestId}`;
          streamingState = {
            content: messageChunk,
            messageId,
          };
          newStreamingMessages.set(requestId, streamingState);

          // 添加到消息列表（作为AI消息）
          const streamingMessage: Message = {
            id: messageId,
            role: 'assistant',
            content: messageChunk,
            sessionId: sessionIdRef.current,
            status: 'streaming', // 标记为流式状态
            createdAt: Date.now(),
            updatedAt: Date.now(),
          };

          setMessages(prev => [...prev, streamingMessage]);
          loggerRef.current.info('📝 创建新的流式消息', { messageId, content: messageChunk });
        } else {
          // 更新现有流式消息
          streamingState.content += messageChunk;

          // 更新消息列表中的对应消息
          setMessages(prev =>
            prev.map(msg => {
              if (streamingState && msg.id === streamingState.messageId) {
                return {
                  ...msg,
                  content: streamingState.content,
                  status: 'streaming', // 始终保持streaming状态，直到收到最终响应
                  updatedAt: Date.now(),
                };
              }
              return msg;
            })
          );

          loggerRef.current.debug('🔄 更新流式消息内容', {
            messageId: streamingState.messageId,
            totalLength: streamingState.content.length,
            isPartial,
          });
        }

        // 如果不是部分消息，清理流式状态
        if (!isPartial) {
          newStreamingMessages.delete(requestId);
          loggerRef.current.info('✅ 流式消息完成', { requestId });
        }

        return newStreamingMessages;
      });

      // 滚动到底部（由ChatWindow组件处理）
    },
    [setMessages, setStreamingMessages, streamingMessages.size]
  );

  // 核心事件绑定逻辑（与Lit版本完全一致）
  useEffect(() => {
    if (!sdk) return;

    const eventBus = sdk.getEventBus();
    const logger = loggerRef.current;
    const sessionId = sessionIdRef.current;

    logger.info('React打招呼页面开始绑定事件', { sessionId });

    // 【关键】监听路由器发送的AI响应事件（与Lit版本完全一致）
    const handleAIResponse = (data: unknown) => {
      const response = data as { message: string; sessionId: string; requestId: string };
      logger.info('收到AI响应事件（非流式）', {
        responseSessionId: response.sessionId,
        currentSessionId: sessionId,
        requestId: response.requestId,
        message: response.message.substring(0, 50),
      });

      if (response.sessionId === sessionId) {
        // 使用函数式更新来检查是否已经有对应的流式消息
        let hasStreamingMessage = false;

        setMessages(prev => {
          const existingStreamingMessage = prev.find(
            msg => msg.id === `streaming_${response.requestId}` && msg.status === 'streaming'
          );

          if (existingStreamingMessage) {
            // 如果已经有流式消息，更新其状态为完成
            hasStreamingMessage = true;
            logger.info('更新流式消息状态为完成', { requestId: response.requestId });
            return prev.map(msg => {
              if (msg.id === `streaming_${response.requestId}`) {
                return {
                  ...msg,
                  content: response.message,
                  status: 'delivered',
                  updatedAt: Date.now(),
                };
              }
              return msg;
            });
          } else {
            // 如果没有流式消息，直接添加完整消息
            logger.info('会话ID匹配，添加AI消息到UI');
            return [
              ...prev,
              {
                id: `ai-${Date.now()}`,
                role: 'assistant',
                content: response.message,
                sessionId: sessionId,
                status: 'delivered',
                createdAt: Date.now(),
                updatedAt: Date.now(),
              },
            ];
          }
        });

        // 清理对应的流式消息状态
        if (hasStreamingMessage) {
          setStreamingMessages(prev => {
            const newMap = new Map(prev);
            newMap.delete(response.requestId);
            return newMap;
          });
        }

        setIsLoading(false);
      } else {
        logger.warn('会话ID不匹配，忽略AI响应', {
          expected: sessionId,
          received: response.sessionId,
        });
      }
    };

    // 【关键】监听路由器发送的用户输入事件（与Lit版本完全一致）
    const handleUserInput = (data: unknown) => {
      const input = data as { userInput: string; sessionId: string; requestId: string };
      logger.info('收到用户输入事件', {
        inputSessionId: input.sessionId,
        currentSessionId: sessionId,
        userInput: input.userInput,
      });

      if (input.sessionId === sessionId) {
        logger.info('会话ID匹配，添加用户消息到UI');
        addMessage({
          id: `user-${Date.now()}`,
          role: 'user',
          content: input.userInput,
          sessionId: sessionId,
          status: 'delivered',
          createdAt: Date.now(),
          updatedAt: Date.now(),
        });
      } else {
        logger.warn('会话ID不匹配，忽略用户输入', {
          expected: sessionId,
          received: input.sessionId,
        });
      }
    };

    // 【关键】监听流式消息片段（实时显示）
    const handleStreamMessageChunk = (data: unknown) => {
      const chunk = data as {
        message: string;
        sessionId: string;
        requestId: string;
        isPartial: boolean;
      };

      logger.debug('📈 GreetingPage收到流式消息片段', {
        message: chunk.message,
        eventSessionId: chunk.sessionId,
        mySessionId: sessionId,
        requestId: chunk.requestId,
        sessionMatch: chunk.sessionId === sessionId,
        messageLength: chunk.message?.length || 0,
        isPartial: chunk.isPartial,
      });

      // 如果sessionId匹配，实时更新消息显示
      if (chunk.sessionId === sessionId && chunk.message && chunk.message.trim()) {
        updateStreamingMessage(chunk.message, chunk.requestId, chunk.isPartial);
      }
    };

    // 【关键】绑定事件（与Lit版本完全一致的事件名）
    eventBus.on('greeting-page:ai-response', handleAIResponse);
    eventBus.on('greeting-page:user-input', handleUserInput);
    eventBus.on('ai:stream-message-chunk', handleStreamMessageChunk);

    // 【关键】通知路由器打招呼页面已显示（与Lit版本完全一致）
    eventBus.emit('component:greeting-page-visible', { sessionId });

    // 【关键】切换到打招呼页面场景（与Lit版本完全一致）
    eventBus.emit('router:switch-scenario', {
      scenario: 'greeting-page',
      sessionId,
    });

    logger.info('✅ React打招呼页面事件绑定完成，场景已切换', { sessionId });

    // 移除硬编码的欢迎消息，避免与AI响应重复

    // 清理函数（与Lit版本完全一致）
    return () => {
      eventBus.off('greeting-page:ai-response', handleAIResponse);
      eventBus.off('greeting-page:user-input', handleUserInput);
      eventBus.off('ai:stream-message-chunk', handleStreamMessageChunk);

      // 【关键】通知路由器打招呼页面已隐藏
      eventBus.emit('component:greeting-page-hidden', { sessionId });

      logger.info('✅ React打招呼页面事件已清理', { sessionId });
    };
  }, [sdk, updateStreamingMessage]);

  // 初始化收音模式、语言和布局状态
  useEffect(() => {
    const savedMode = localStorage.getItem('greeting-page-microphone-mode') as 'button' | 'lip';
    if (savedMode && (savedMode === 'button' || savedMode === 'lip')) {
      setMicrophoneMode(savedMode);
    } else {
      // 第一次进入时，默认设置为唇动收音模式并保存到localStorage
      setMicrophoneMode('lip');
      localStorage.setItem('greeting-page-microphone-mode', 'lip');
    }

    const savedLanguage = localStorage.getItem('websdk-language') as 'mandarin' | 'chuanyu';
    const finalLanguage =
      savedLanguage && (savedLanguage === 'mandarin' || savedLanguage === 'chuanyu')
        ? savedLanguage
        : 'mandarin';

    // 初始化页面状态
    if (sdk && sdk.getStatus().isReady) {
      const pageStateManager = sdk.getPageStateManager();
      setPageState(pageStateManager.getCurrentState());
    }

    setLanguage(finalLanguage);

    if (!savedLanguage) {
      localStorage.setItem('websdk-language', 'mandarin');
    }

    // 通知ServiceCoordinator当前语言状态（GreetingPage优先级）
    if (sdk && sdk.getStatus().isReady) {
      sdk.getEventBus().emit('tts:language-change', { language: finalLanguage });
      loggerRef.current.info('🗣️ GreetingPage初始化时通知语言状态', { language: finalLanguage });
    }

    const savedLayoutMode = localStorage.getItem('greeting-page-layout-mode') as
      | 'standard'
      | 'care';
    if (savedLayoutMode && (savedLayoutMode === 'standard' || savedLayoutMode === 'care')) {
      setLayoutMode(savedLayoutMode);
    }
  }, [sdk]);

  // 监听收音模式变化，确保VideoPlayer组件同步更新
  useEffect(() => {
    if (videoPlayerRef.current && microphoneMode) {
      videoPlayerRef.current.switchMicrophoneMode(microphoneMode);
    }
  }, [microphoneMode]);

  // 监听页面状态变化
  useEffect(() => {
    if (!sdk || !sdk.getStatus().isReady) {
      return;
    }

    const eventBus = sdk.getEventBus();
    const logger = loggerRef.current;

    const handlePageStateChange = (data: unknown) => {
      const eventData = data as PageStateChangeEvent;
      logger.info('页面状态已变化', {
        from: eventData.previousState,
        to: eventData.currentState,
        timestamp: eventData.timestamp,
      });

      setPageState(eventData.currentState);
    };

    // 监听页面状态变化事件
    eventBus.on('page-state:changed', handlePageStateChange);

    return () => {
      eventBus.off('page-state:changed', handlePageStateChange);
    };
  }, [sdk]);

  // 监听其他组件的语言变化
  useEffect(() => {
    if (!sdk || !sdk.getStatus().isReady) {
      return;
    }

    const eventBus = sdk.getEventBus();
    const logger = loggerRef.current;

    const handleLanguageChangeFromOtherComponent = (data: unknown) => {
      const eventData = data as { language: string; source: string };
      const { language, source } = eventData;
      // 只响应来自其他组件的语言变化，避免循环
      if (
        source !== 'greeting-page' &&
        language &&
        (language === 'mandarin' || language === 'chuanyu')
      ) {
        logger.info('🔄 GreetingPage收到其他组件的语言变化通知', { language, source });
        setLanguage(language);
        // 同步更新localStorage
        localStorage.setItem('websdk-language', language);
      }
    };

    eventBus.on('ui:language-change', handleLanguageChangeFromOtherComponent);

    return () => {
      eventBus.off('ui:language-change', handleLanguageChangeFromOtherComponent);
    };
  }, [sdk]);

  // 处理收音模式切换
  const handleMicrophoneModeChange = async (mode: 'button' | 'lip') => {
    const logger = loggerRef.current;
    logger.info('🎤 GreetingPage收音模式切换', { mode });

    setMicrophoneMode(mode);
    videoPlayerRef.current?.switchMicrophoneMode(mode);

    // 存储到localStorage以保持状态
    localStorage.setItem('greeting-page-microphone-mode', mode);

    // 发送JSON-RPC请求通知HKSTT服务收音模式变化
    try {
      const speakMode = mode === 'lip' ? 0 : 1; // 0=唇动收音，1=点击收音
      const requestId = generateUUID();

      logger.info('📤 发送收音模式通知到HKSTT服务', { mode, speakMode, requestId });

      const response = await sdk.sendJsonRpcMessage({
        jsonrpc: '2.0',
        method: 'speakMode',
        params: {
          speakMode: speakMode,
        },
        id: requestId,
      });

      logger.info('✅ 收音模式通知发送成功', { mode, speakMode, response });
    } catch (error) {
      logger.error('❌ 发送收音模式通知失败', { mode, error });
      // 不影响UI切换，只记录错误
    }
  };

  // 处理语言模式切换
  const handleLanguageChange = (newLanguage: 'mandarin' | 'chuanyu') => {
    const logger = loggerRef.current;
    logger.info('🗣️ GreetingPage语言模式切换', { language: newLanguage });

    setLanguage(newLanguage);

    // 存储到localStorage以保持状态
    localStorage.setItem('websdk-language', newLanguage);

    // 通过EventBus通知TTS服务语言模式变化
    if (sdk && sdk.getStatus().isReady) {
      sdk.getEventBus().emit('tts:language-change', { language: newLanguage });
      // 通知其他组件语言状态变化
      sdk
        .getEventBus()
        .emit('ui:language-change', { language: newLanguage, source: 'greeting-page' });
    }
  };

  // 处理布局模式切换
  const handleLayoutModeChange = (newMode: 'standard' | 'care') => {
    const logger = loggerRef.current;
    logger.info('🎨 布局模式切换', { mode: newMode });

    setLayoutMode(newMode);

    // 存储到localStorage以保持状态
    localStorage.setItem('greeting-page-layout-mode', newMode);
  };

  // 处理业务选择
  const handleBusinessSelect = (businessName: string) => {
    const logger = loggerRef.current;
    logger.info('📋 业务选择', { business: businessName });

    // 注意：不再自动发送消息到AI，只记录业务选择
    // 业务按钮的点击事件已经通过 QuickActions 组件的事件机制发送
    // 外部应用可以通过 sdk.onBusinessButtonClick() 监听这些事件并决定如何处理
    logger.info('业务选择已记录，事件已通过按钮组件发送', { business: businessName });
  };

  // 处理返回按钮（与Lit版本完全一致）
  const handleBack = () => {
    const logger = loggerRef.current;
    const sessionId = sessionIdRef.current;

    logger.info('用户点击返回按钮', { sessionId });

    if (sdk && sdk.getStatus().isReady) {
      // 【关键】发送返回事件（与Lit版本完全一致）
      sdk.getEventBus().emit('greeting-page:back', { sessionId });
      logger.info('✅ 已发送返回事件');
    }

    // 调用外部关闭回调
    onClose?.();
  };

  // 处理TTS自动播放被阻止事件
  useEffect(() => {
    const logger = loggerRef.current;

    if (!sdk || !sdk.getStatus().isReady) {
      return;
    }

    const eventBus = sdk.getEventBus();

    // 监听TTS自动播放被阻止事件
    const handleAutoplayBlocked = () => {
      logger.info('TTS自动播放被阻止，显示用户提示');
      setShowAutoplayNotification(true);
    };

    // 监听TTS自动播放恢复事件
    const handleAutoplayResumed = () => {
      logger.info('TTS自动播放已恢复，隐藏用户提示');
      setShowAutoplayNotification(false);
    };

    // 绑定事件监听器
    eventBus.on('tts:autoplay-blocked', handleAutoplayBlocked);
    eventBus.on('tts:autoplay-resumed', handleAutoplayResumed);

    // 清理函数
    return () => {
      eventBus.off('tts:autoplay-blocked', handleAutoplayBlocked);
      eventBus.off('tts:autoplay-resumed', handleAutoplayResumed);
    };
  }, [sdk]);

  // 处理启用音频播放
  const handleEnableAudio = () => {
    const logger = loggerRef.current;
    logger.info('🎵 用户点击启用音频播放');

    if (!sdk || !sdk.getStatus().isReady) {
      logger.warn('SDK未就绪，无法启用音频');
      return;
    }

    // 立即在同步执行阶段通知用户已交互（关键：必须在真实用户事件的同步阶段）
    const eventBus = sdk.getEventBus();
    eventBus.emit('user:interaction-detected', {
      source: 'autoplay-notification-click',
      timestamp: Date.now(),
    });
    logger.info('🎵 用户交互事件已立即发送');

    // 方法1：创建一个真实的音频并播放来激活音频上下文
    const audio = new Audio();
    // 使用一个很短的静音音频来激活音频上下文
    audio.src =
      'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmHgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';

    const playPromise = audio.play();

    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          logger.info('✅ 音频上下文已激活');
          audio.pause();
          audio.currentTime = 0;
          logger.info('🎵 音频播放权限已获取，TTS应该可以自动播放了');
        })
        .catch(error => {
          logger.warn('音频播放失败，但用户交互已记录', { error });
        });
    }

    // 隐藏提示
    setShowAutoplayNotification(false);

    logger.info('🎵 处理完成，等待TTS自动播放');
  };

  // 关爱模式样式变量
  const careModeCSSVariables: React.CSSProperties =
    layoutMode === 'care'
      ? ({
          '--font-size-base': '18px',
          '--font-size-large': '24px',
          '--font-size-xl': '30px',
          '--button-height': '60px',
          '--button-padding': '16px 24px',
          '--spacing-base': '16px',
          '--spacing-large': '24px',
          '--border-radius': '8px',
          '--text-color': '#000000',
          '--bg-color': '#ffffff',
          '--primary-color': '#1677ff',
          '--border-color': '#d9d9d9',
        } as React.CSSProperties)
      : {};

  return (
    <div
      className={`greeting-page-container ${layoutMode === 'care' ? 'care-mode' : ''}`}
      data-component="greeting-page"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
        zIndex: 9999,
        display: 'flex',
        flexDirection: 'column',
        padding: 0,
        fontFamily:
          '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif',
        fontSize: layoutMode === 'care' ? '18px' : '14px',
        lineHeight: layoutMode === 'care' ? '1.5' : '1.4',
        // 确保滚动条不影响布局稳定性
        scrollbarGutter: 'stable',
        ...careModeCSSVariables,
      }}
    >
      {/* 顶部导航栏 */}
      <TopNavigation
        onBack={handleBack}
        microphoneMode={microphoneMode}
        onMicrophoneModeChange={handleMicrophoneModeChange}
        language={language}
        onLanguageChange={handleLanguageChange}
        layoutMode={layoutMode}
        onLayoutModeChange={handleLayoutModeChange}
      />

      {/* 主内容区域 - 三列布局 */}
      <div
        className="content-area"
        style={{
          flex: 1,
          display: 'flex',
          gap: '20px',
          padding: '20px',
          height: 'calc(100vh - 80px)',
        }}
      >
        {/* 左侧业务展示区 */}
        <div
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            gap: layoutMode === 'care' ? '24px' : '16px',
            fontSize: layoutMode === 'care' ? '18px' : '14px',
          }}
        >
          {/* 常用功能区 - 只在全功能页面显示 */}
          {pageState === 'WaitCardPage' && (
            <QuickActions
              onActionClick={action => {
                loggerRef.current.info('点击常用功能', { action });
              }}
              onBusinessSelect={handleBusinessSelect}
              layoutMode={layoutMode}
              sdk={sdk} // 传递SDK实例
              style={{
                fontSize: layoutMode === 'care' ? '20px' : '14px',
                // 标准模式下恢复2:3比例关系中的2份高度
                flex: layoutMode === 'standard' ? '2' : undefined,
              }}
            />
          )}

          {/* 全部业务区 - 根据页面状态和布局模式显示 */}
          {(layoutMode === 'standard' || pageState !== 'WaitCardPage') && (
            <AllServices
              layoutMode={layoutMode} // 传递布局模式
              pageState={pageState} // 传递页面状态
              sdk={sdk} // 传递SDK实例
              onServiceClick={service => {
                loggerRef.current.info('点击业务服务', { service });
              }}
              style={{
                // 根据页面状态调整flex值
                flex: pageState === 'WaitCardPage' && layoutMode === 'standard' ? '3' : '1',
              }}
            />
          )}
        </div>

        {/* 中间虚拟人展示区 */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <Card
            style={{
              flex: '1.2',
              background: 'rgba(255, 255, 255, 0.8)',
              backdropFilter: 'blur(20px) saturate(180%)',
              border: '1px solid rgba(30, 41, 59, 0.1)',
              borderRadius: '20px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'flex-start', // 改为从顶部开始，不居中
              position: 'relative',
              overflow: 'hidden', // 确保内容不会溢出圆角
            }}
          >
            <CardContent
              style={{
                padding: '0', // 完全移除内边距，让数字人直接贴着顶部
                textAlign: 'center',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'flex-start', // 从顶部开始布局
                gap: '0', // 完全移除间距
                width: '100%',
                height: '100%',
              }}
            >
              {/* 虚拟人视频区域 */}
              <VideoPlayer
                ref={videoPlayerRef}
                sdk={sdk}
                microphoneMode={microphoneMode}
                layoutMode={layoutMode}
                style={{
                  flex: '1', // 让数字人占据更多垂直空间
                  width: '100%',
                  minHeight: '300px', // 增加最小高度，让数字人更大
                  borderRadius: '20px 20px 0 0', // 只保留顶部圆角，与卡片顶部对齐
                  // 减小顶部间距：关爱模式10px，标准模式完全无间距
                  paddingTop: layoutMode === 'care' ? '10px' : '0',
                }}
              />

              {/* 虚拟人描述文案 - 卡片化显示 */}
              <div style={{ padding: layoutMode === 'care' ? '16px' : '12px', width: '100%' }}>
                <AvatarFeatures layoutMode={layoutMode} />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧聊天和推荐区 */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column', gap: '16px' }}>
          {/* 对话窗口 */}
          <ChatWindow
            sdk={sdk}
            sessionId={sessionIdRef.current}
            messages={messages}
            isLoading={isLoading}
            onMessagesChange={setMessages}
            onLoadingChange={setIsLoading}
            layoutMode={layoutMode} // 传递布局模式
            style={{ flex: 1 }}
          />

          {/* 智能推荐区 */}
          <RecommendationCards
            layoutMode={layoutMode} // 传递布局模式
            onItemClick={item => {
              // 处理推荐项目点击
              loggerRef.current.info('点击推荐项目', { item });
            }}
          />
        </div>
      </div>

      {/* TTS自动播放被阻止提示 */}
      <AutoplayBlockedNotification
        open={showAutoplayNotification}
        onEnableAudio={handleEnableAudio}
      />
    </div>
  );
};

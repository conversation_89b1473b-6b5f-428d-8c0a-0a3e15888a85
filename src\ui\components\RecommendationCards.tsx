/**
 * 推荐卡片组件
 * 负责显示智能推荐功能
 */

import {
  DollarSign,
  ArrowRightLeft,
  Wallet,
  Star,
  Gift,
  BookOpen,
  Coins,
  Receipt,
} from 'lucide-react';
import React from 'react';

import { Card, CardContent } from '../../components/ui/card';

interface RecommendationItem {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  category: string;
}

interface RecommendationCardsProps {
  onItemClick?: (item: RecommendationItem) => void;
  className?: string;
  style?: React.CSSProperties;
  layoutMode?: 'standard' | 'care'; // 添加布局模式支持
}

export const RecommendationCards: React.FC<RecommendationCardsProps> = ({
  onItemClick,
  className,
  style,
  layoutMode = 'standard', // 默认为标准模式
}) => {
  // 推荐项目数据
  const recommendationItems: RecommendationItem[] = [
    {
      id: 'account-inquiry',
      title: '账户查询',
      description: '查看账户余额信息',
      icon: <Wallet size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)',
      category: '账户服务',
    },
    {
      id: 'transaction-detail',
      title: '交易明细查询',
      description: '查看交易记录明细',
      icon: <ArrowRightLeft size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #34C759 0%, #30D158 100%)',
      category: '查询服务',
    },
    {
      id: 'loan-repayment',
      title: '贷款还款查询',
      description: '查询贷款还款记录',
      icon: <DollarSign size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #FF9500 0%, #FF8C00 100%)',
      category: '贷款服务',
    },
    {
      id: 'transfer-receipt',
      title: '现金转账交易凭证',
      description: '打印转账凭证',
      icon: <Receipt size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #AF52DE 0%, #BF5AF2 100%)',
      category: '凭证服务',
    },
    {
      id: 'points-inquiry',
      title: '积分查询',
      description: '查看积分余额',
      icon: <Coins size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #FF3B30 0%, #FF6B6B 100%)',
      category: '积分服务',
    },
    {
      id: 'points-consumption',
      title: '积分消费',
      description: '积分兑换商品',
      icon: <Gift size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #32D74B 0%, #30B050 100%)',
      category: '积分服务',
    },
    {
      id: 'happy-bean-inquiry',
      title: '幸福豆余额查询',
      description: '查看幸福豆余额',
      icon: <Star size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #5856D6 0%, #7B68EE 100%)',
      category: '积分服务',
    },
    {
      id: 'passbook-update',
      title: '存折补登',
      description: '存折补登服务',
      icon: <BookOpen size={layoutMode === 'care' ? 24 : 20} />,
      color: 'linear-gradient(135deg, #FF9F0A 0%, #FFB347 100%)',
      category: '存折服务',
    },
  ];

  const handleItemClick = (item: RecommendationItem) => {
    onItemClick?.(item);
  };

  return (
    <Card
      className={className}
      style={{
        backgroundColor: 'rgba(255, 255, 255, 0.8)', // 改为更不透明的白色背景
        backdropFilter: 'blur(20px) saturate(180%)',
        border: '1px solid rgba(30, 41, 59, 0.1)', // 改为与其他组件一致的边框色
        borderRadius: '20px',
        boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.06)',
        width: '100%', // 确保卡片不会超出父容器宽度
        maxWidth: '100%', // 防止内容撑破容器
        overflow: 'hidden', // 防止内容溢出
        display: 'flex',
        flexDirection: 'column',
        minHeight: 0, // 确保flex子元素可以收缩
        height: '100%', // 占满父容器分配的高度
        boxSizing: 'border-box', // 确保padding和border包含在尺寸内
        ...style,
      }}
    >
      <div
        style={{
          padding: '20px 20px 16px 20px',
          borderBottom: '1px solid rgba(30, 41, 59, 0.1)', // 改为与其他组件一致的分割线色
          flexShrink: 0, // 防止标题区域被压缩
          width: '100%',
          maxWidth: '100%',
          overflow: 'hidden', // 防止标题溢出
        }}
      >
        <h3
          style={{
            fontSize: layoutMode === 'care' ? '28px' : '22px', // 关爱模式28px，普通模式22px（标题文字）
            fontWeight: '700',
            color: '#1e293b',
            margin: 0,
            display: 'flex',
            alignItems: 'center',
            gap: layoutMode === 'care' ? '10px' : '8px', // 关爱模式增加间距
            width: '100%',
            maxWidth: '100%',
            overflow: 'hidden', // 防止标题文字溢出
            textOverflow: 'ellipsis', // 长文本显示省略号
            whiteSpace: 'nowrap', // 防止标题换行
          }}
        >
          <Star
            size={layoutMode === 'care' ? 24 : 20}
            style={{ color: '#FFD700', flexShrink: 0 }}
          />{' '}
          {/* 关爱模式增大图标，防止图标被压缩 */}
          智能推荐
        </h3>
      </div>

      <CardContent
        style={{
          padding: '20px',
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0, // 确保flex子元素可以收缩
          width: '100%',
          maxWidth: '100%',
          overflow: 'hidden', // 防止内容溢出
        }}
      >
        <div
          className="scrollable-area" // 添加滚动条样式类
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: layoutMode === 'care' ? '12px' : '8px', // 关爱模式增加间距
            // 恢复合适的高度设置，确保有足够空间显示卡片并支持滚动
            flex: 1,
            minHeight: 0, // 确保可以收缩
            height: '100%', // 占满父容器高度
            overflowY: 'auto',
            overflowX: 'hidden', // 防止水平溢出
            // 根据布局模式调整padding
            paddingRight: layoutMode === 'care' ? '4px' : '8px', // 标准模式需要更多右侧空间
            paddingLeft: '0px', // 确保左侧没有额外padding
            width: '100%',
            maxWidth: '100%',
            boxSizing: 'border-box', // 确保padding包含在尺寸内
          }}
        >
          {recommendationItems.map(item => (
            <div
              key={item.id}
              onClick={() => handleItemClick(item)}
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: layoutMode === 'care' ? '16px' : '12px', // 标准模式减少间距
                padding: layoutMode === 'care' ? '20px 16px' : '16px 8px', // 标准模式进一步减少左右内边距
                borderRadius: layoutMode === 'care' ? '12px' : '8px', // 关爱模式增大圆角
                background: 'rgba(255, 255, 255, 0.8)',
                border: '1px solid rgba(30, 41, 59, 0.1)',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                // 根据布局模式调整宽度计算
                width: layoutMode === 'care' ? 'calc(100% - 4px)' : 'calc(100% - 8px)', // 标准模式需要更多空间
                maxWidth: layoutMode === 'care' ? 'calc(100% - 4px)' : 'calc(100% - 8px)',
                minWidth: 0, // 允许收缩
                overflow: 'hidden', // 防止内容溢出
                flexShrink: 0, // 防止卡片被压缩
                boxSizing: 'border-box', // 确保padding和border包含在width内
                minHeight: layoutMode === 'care' ? '80px' : '70px', // 设置最小高度，确保卡片有足够高度
              }}
              onMouseEnter={e => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.95)';
                e.currentTarget.style.transform = 'translateX(4px)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
              }}
              onMouseLeave={e => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.8)';
                e.currentTarget.style.transform = 'translateX(0)';
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.05)';
              }}
            >
              <div
                style={{
                  width: layoutMode === 'care' ? '48px' : '40px', // 关爱模式增大图标容器
                  height: layoutMode === 'care' ? '48px' : '40px',
                  borderRadius: layoutMode === 'care' ? '12px' : '8px', // 关爱模式增大圆角
                  background: `${item.color}20`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0,
                }}
              >
                <div
                  style={{ color: item.color.includes('linear-gradient') ? '#007AFF' : item.color }}
                >
                  {item.icon}
                </div>
              </div>
              <div
                style={{
                  flex: 1,
                  minWidth: 0, // 允许文本区域收缩
                  overflow: 'hidden', // 防止文本溢出
                }}
              >
                <div
                  style={{
                    fontSize: layoutMode === 'care' ? '24px' : '18px', // 关爱模式24px，普通模式18px（普通文字）
                    fontWeight: '600',
                    color: '#1e293b',
                    marginBottom: layoutMode === 'care' ? '6px' : '4px', // 增加标题和描述之间的间距
                    lineHeight: '1.4', // 增加行高，让文字更舒适
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap', // 标题单行显示，超出显示省略号
                    width: '100%',
                  }}
                >
                  {item.title}
                </div>
                <div
                  style={{
                    fontSize: layoutMode === 'care' ? '20px' : '16px', // 关爱模式20px，普通模式16px（次要文字）
                    color: '#64748b',
                    lineHeight: '1.5', // 增加行高，让文字更舒适
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap', // 描述单行显示，超出显示省略号
                    width: '100%',
                  }}
                >
                  {item.description}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
